[program:client-schedule-work]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php artisan schedule:work
directory=/home/<USER>/sd-client/
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-schedule-expiry]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_exp_notif_sched_jobs --queue=default,send --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-mail-jobs]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work mail_jobs --queue=DOMAIN_EXPIRY_NOTICE,PAYMENT_INVOICE,REFUND_SUCCESS,IDENTITY_NOTICE,DOMAIN_REDEMPTION_NOTICE,ACCOUNT_CREDIT --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-registration]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_registration_jobs --queue=VERISIGN-REGISTRATION,PIR-REGISTRATION --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-renewal]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_renewal_jobs --queue=VERISIGN-RENEW,PIR-RENEW --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-update]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_update_jobs --queue=VERISIGN-UPDATE,PIR-UPDATE --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-payment]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work payment_jobs --queue=default,email,refund --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-contacts-update]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_contacts_update_jobs --queue=VERISIGN-PUSH,PIR-PUSH --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-contact-registration]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work contact_registration_jobs --queue=VERISIGN-CONTACT-CREATE,PIR-CONTACT-CREATE --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-contact-update]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work contact_update_jobs --queue=VERISIGN-CONTACT-UPDATE,PIR-CONTACT-UPDATE --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-update-on-login]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work update_on_login_jobs --queue=PUSH-REQUEST-EXPIRED --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-transfer]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_transfer_jobs --queue=VERISIGN-TRANSFER,PIR-TRANSFER --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-redemption]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_redemption_jobs --queue=VERISIGN-REDEMPTION,PIR-REDEMPTION --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-cancel-domain-transfer]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work cancel_domain_transfer_jobs --queue=default --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-send-transfer-request-response]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work send_transfer_request_response_jobs --queue=VERISIGN-TRANSFER-RESPONSE,PIR-TRANSFER-RESPONSE --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-poll-update-domain-transfer]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work poll_update_domain_transfer_jobs --queue=VERISIGN-POLL-TRANSFER-UPDATE,PIR-POLL-TRANSFER-UPDATE --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-authcode-request]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_authcode_request_jobs --queue=default --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-authcode-update]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_authcode_update_jobs --queue=VERISIGN-AUTHCODE-UPDATE,PIR-AUTHCODE-UPDATE --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-domain-refresh]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_refresh_jobs --queue=VERISIGN-REFRESH,PIR-REFRESH --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-otp_mail-jobs]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work otp_mail_jobs --queue=email,authenticator_app,recovery_code,passkey --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-user-domain-export-jobs]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work user_domain_export_jobs --queue=default --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-user-authentication-attempts-jobs]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work authentication_attempts_jobs --queue=login_attempts_exceeded --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-webhook-stripe-identity]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work stripe_identity_jobs --queue=default --timeout=30 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-market-place]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work market_place_jobs --queue=default,afternic --tries=3 --timeout=120 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180

[program:client-dc-api-jobs]
process_name=%(program_name)s_%(process_num)02d
command=/usr/bin/php %(here)s/artisan queue:work domain_classification_api_jobs --queue=insert,update --tries=3 --timeout=120 --max-time=900 --stop-when-empty
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=%(here)s/storage/logs/worker.log
startsecs=0
stopwaitsecs=180