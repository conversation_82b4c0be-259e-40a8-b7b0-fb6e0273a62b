<?php if (isset($component)) { $__componentOriginalaa758e6a82983efcbf593f765e026bd9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa758e6a82983efcbf593f765e026bd9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => $__env->getContainer()->make(Illuminate\View\Factory::class)->make('mail::message'),'data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mail::message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
## <?php echo new \Illuminate\Support\EncodedHtmlString($greeting); ?>


<p>
We are pleased to inform you that your recent fund deposit has been successfully added to your account. Below are the transaction details:
</p>

<?php $__env->startComponent('mail::table'); ?>
| | |
| :--------- | :---------|
| Date: <?php echo new \Illuminate\Support\EncodedHtmlString(\Carbon\Carbon::parse($date)->format('F j, Y h:i:s A')); ?> | |
| Transaction ID: <?php echo new \Illuminate\Support\EncodedHtmlString($transactionId ?? 'N/A'); ?> | |
| Payment Method: <?php echo new \Illuminate\Support\EncodedHtmlString($paymentMethod); ?> | |
| Total Paid: | $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format(($paymentServiceObj->paid_amount ?? $amount ?? '0.00'),2 )); ?> |
| Miscellaneous Fees: | $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($paymentServiceObj->service_fee ?? '0.00', 2)); ?> |
| **Amount Added:**  | **$<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($amount, 2)); ?>** |
| **New Account Balance:**  | **$<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($balance,2)); ?>** |
<?php echo $__env->renderComponent(); ?>

<p>
You can view this transaction in your account dashboard by visiting <i><a href="<?php echo new \Illuminate\Support\EncodedHtmlString($redirectUrl); ?>">this link</a></i>.
</p>

<p>
    If you have any questions or need assistance, feel free to contact us at <i><a href="<?php echo new \Illuminate\Support\EncodedHtmlString($supportUrl); ?>">StrangeDomains.com</a></i>.
    Or call us at <strong><?php echo new \Illuminate\Support\EncodedHtmlString($phoneNumber); ?></strong>.
</p>

### Thank you for choosing <?php echo new \Illuminate\Support\EncodedHtmlString($senderName); ?>.

## Sincerely,
## <?php echo new \Illuminate\Support\EncodedHtmlString($senderName); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $attributes = $__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $component = $__componentOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?><?php /**PATH C:\1xampp\htdocs\sd-client\resources\views/Mails/Payment/AccountDebitSuccess.blade.php ENDPATH**/ ?>