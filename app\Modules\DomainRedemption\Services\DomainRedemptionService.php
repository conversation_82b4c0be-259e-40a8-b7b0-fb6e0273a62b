<?php

namespace App\Modules\DomainRedemption\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Services\StripeLimiter;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\DomainRedemption\Services\JobServices\JobRedemptionDispatchService;
use App\Modules\DomainRedemption\Services\RedemptionPaymentService;
use Illuminate\Support\Facades\Config;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Traits\CursorPaginate;

class DomainRedemptionService
{
    use UserLoggerTrait, CursorPaginate;

    private $dispatchDelayInSeconds = 180;
    private $pageLimit = 20;

    public static function instance()
    {
        return new self;
    }

    public function getAllData(array $filters = [])
    {
        $query = $this->buildQuery();
        $this->applyFilters($query, $filters);

        // Set page limit from filters or use default
        $pageLimit = $filters['limit'] ?? $this->pageLimit;

        // Get paginated results
        $paginatedResults = $query->paginate($pageLimit)->withQueryString();

        // Return cursor paginated format
        return $this->cursor($paginatedResults, $this->buildParamArray($filters));
    }

    public function getDomainById(int $id)
    {
        $query = $this->buildQuery();

        return $query->where('redemption_orders.id', $id)->first();
    }

    private function buildQuery()
    {
        return DB::table('redemption_orders')
            ->select([
                'redemption_orders.id',
                'redemption_orders.user_id',
                'redemption_orders.domain_id',
                'redemption_orders.total_amount',
                'redemption_orders.uuid',
                'redemption_orders.paid_at',
                'redemption_orders.valid_until',
                'redemption_orders.note',
                'redemption_orders.created_at',
                'redemption_orders.updated_at',
                'domains.name as domain_name',
                'domains.status as domain_status',
                'domains.expiry_date',
                DB::raw("CASE
                    WHEN redemption_orders.paid_at IS NULL THEN 'not_paid'
                    WHEN redemption_orders.paid_at IS NOT NULL AND redemption_orders.deleted_at IS NULL THEN 'in_process'
                    WHEN redemption_orders.deleted_at IS NOT NULL AND redemption_orders.paid_at IS NOT NULL THEN 'completed'
                    ELSE 'unknown'
                END as status")
            ])
            ->join('domains', 'domains.id', '=', 'redemption_orders.domain_id')
            ->where('redemption_orders.user_id', Auth::id());
    }

    private function applyFilters($query, array $filters)
    {
        if (!empty($filters['search'])) {
            $query->where('domains.name', 'ilike', '%' . $filters['search'] . '%');
        }

        if (!empty($filters['status']) && $filters['status'] !== 'all') {
            switch ($filters['status']) {
                case 'not_paid':
                    $query->whereNull('redemption_orders.paid_at');
                    break;
                case 'in_process':
                    $query->whereNotNull('redemption_orders.paid_at')
                          ->whereNull('redemption_orders.deleted_at');
                    break;
                case 'completed':
                    $query->whereNotNull('redemption_orders.paid_at')
                          ->whereNotNull('redemption_orders.deleted_at');
                    break;
            }
        }

        if (!empty($filters['orderby'])) {
            switch ($filters['orderby']) {
                case 'domain_name':
                    $query->orderBy('domains.name', 'asc');
                    break;
                case 'total_amount':
                    $query->orderBy('redemption_orders.total_amount', 'desc');
                    break;
                case 'created_at':
                default:
                    $query->orderBy('redemption_orders.created_at', 'desc');
                    break;
            }
        } else {
            // Default ordering
            $query->orderBy('redemption_orders.created_at', 'desc');
        }

        return $query;
    }

    private function buildParamArray(array $filters): array
    {
        $params = [];

        if (!empty($filters['search'])) {
            $params[] = 'search=' . urlencode($filters['search']);
        }

        if (!empty($filters['status']) && $filters['status'] !== 'all') {
            $params[] = 'status=' . $filters['status'];
        }

        if (!empty($filters['orderby'])) {
            $params[] = 'orderby=' . $filters['orderby'];
        }

        if (!empty($filters['limit'])) {
            $params[] = 'limit=' . $filters['limit'];
        }

        return $params;
    }

    public function getRedemptionPaymentData(array $request): array
    {
        $domain = $request['domain'];
        $other_fees = $this->getRedemptionFees($domain);
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($other_fees['bill_total'] ?? 0);
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($stripeFeeObj['gross_amount'] ?? $other_fees['bill_total']);
        $setupIntents = PaymentIntentProvider::instance()->create($payment);
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        $redemptionPaymentHistory = RedemptionPaymentService::instance()->getUserRedemptionPayments($this->getUserId());

        return [
            'domain' => $domain,
            'other_fees' => $other_fees,
            'secret' => $setupIntents->client_secret,
            'intent' => $setupIntents->id,
            'promise' => Config::get('stripe.publishable_key'),
            'account_credit_balance' => $accountCredit->running_balance ?? 0,
            'stripeFeeObj' => $stripeFeeObj,
            'payment_history' => $redemptionPaymentHistory,
        ];
    }

    public function processRedemption(array $request): string
    {
        StripeLimiter::instance()->clearAttempt();

        $domain = json_decode(json_encode($request['domains'][0]), false);
        $userId = $this->getUserId();

        $paymentData = $this->preparePaymentData($request);
        $redemptionPayment = RedemptionPaymentService::instance()->createRedemptionPayment(
            $domain->id,
            $paymentData,
            $userId,
            $request['payment_service_type']
        );


        $this->updateRedemptionOrderStatus($domain);

        $refundDetails = [
            'redemption_order_id' => $domain->id,
            'payment_service_id' => $redemptionPayment['payment_service']->id,
            'payment_service_type' => $request['payment_service_type'],
        ];

        $this->dispatchRedemptionJob($domain, $refundDetails);
        ScheduleDomainExpiryNotice::dispatch($userId)->delay($this->dispatchDelayInSeconds);

        return (string) ($redemptionPayment['payment_summary_id'] ?? $redemptionPayment['payment_service']->id);
    }

    private function getRedemptionFees(object $domain): array
    {
        $redemptionFee = $this->getRedemptionFeeAmount($domain);
        $icannFee = PaymentFeeService::getIcannFee();

        $billTotal = $redemptionFee + $icannFee;

        return [
            'redemption_fee' => $redemptionFee,
            'redemption_total' => $redemptionFee,
            'icann_fee' => $icannFee,
            'bill_total' => $billTotal,
            'domain_count' => 1,
            'year_sum' => 1,
        ];
    }

    private function getRedemptionFeeAmount(object $domain): float
    {
        return $domain->total_amount;
    }

    private function preparePaymentData(array $request): array
    {
        $otherFees = json_decode(json_encode($request['other_fees']), true);

        return [
            'user_id' => $this->getUserId(),
            'gross_amount' => $otherFees['bill_total'] ?? 0,
            'net_amount' => $otherFees['bill_total'] ?? 0, 
            'service_fee' => 0,
            'bill_amount' => $otherFees['bill_total'] ?? 0, 
            'total_amount' => $otherFees['bill_total'] ?? 0,
            'redemption_fee' => $otherFees['redemption_total'] ?? 0,
            'icann_fee' => $otherFees['icann_fee'] ?? 0,
            'stripe_fee' => $otherFees['stripe_fee'] ?? 0,
            'payment_intent' => $request['intent'] ?? null,
        ];
    }

    private function createPaymentPayload(array $request)
    {
        $otherFees = json_decode(json_encode($request['other_fees']), true);
        $domain = json_decode(json_encode($request['domains'][0]), false);

        $invoice = PaymentInvoiceService::instance()->createInvoicePayload(FeeType::REDEMPTION, $this->getUserId(), $otherFees, $request['intent'] ?? null);
        $nodeInvoice = PaymentInvoiceService::instance()->createNodeInvoicePayload(FeeType::REDEMPTION, collect([$domain]), $otherFees);

        return PaymentInvoiceService::instance()->createPaymentPayload($invoice, $nodeInvoice);
    }

    private function updateRedemptionOrderStatus(object $domain)
    {
        DB::table('redemption_orders')
            ->where('id', $domain->id)
            ->where('user_id', $this->getUserId())
            ->update([
                'paid_at' => now(),
                'updated_at' => now()
            ]);
    }

    private function dispatchRedemptionJob(object $domain, array $refundDetails): void
    {
        JobRedemptionDispatchService::instance()->redemptionEppDispatch($domain, $this->getUserId(), $refundDetails, 60);
    }

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        return Auth::user()->email ?? 'Unauthorized';
    }



}