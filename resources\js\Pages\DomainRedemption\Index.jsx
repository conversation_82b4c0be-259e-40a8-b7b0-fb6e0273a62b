//* PACKAGES
import React, { useState, useEffect } from "react";
import { router, useForm, usePage } from "@inertiajs/react";

//* ICONS
import { TbSortAscending2, TbSortDescending2 } from "react-icons/tb";
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { MdAdd, MdOutlineSettings, MdOutlineFilterAlt } from "react-icons/md";

//* LAYOUTS
import UserLayout from "@/Layouts/UserLayout";

//* COMPONENTS
import Item from "@/Components/DomainRedemption/Item";
import Filter from "@/Components/DomainRedemption/Filter";
import DomainNotFound from "@/Components/Push/DomainNotFound";
import LoaderSpinner from "@/Components/LoaderSpinner";
export default function Index({ items = [] }) {
    const [hasSpinner, setSpinner] = useState(false);

    const hasItem = items.length > 0;

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const query = route().params;
    const limit = parseInt(query.limit) || 20;

    const handleLimitChange = (e) => {
        router.get(
            route("domain-redemption"),
            {
                ...route().params,
                limit: e.target.value,
                page: 1,
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
            }
        );
    };

    return (
        <UserLayout postRouteName={"domain-redemption"}>
            <div className="mx-auto container max-w-[1000px] mt-20 flex flex-col space-y-4">
                <div>
                    <h1 className="text-3xl font-bold mb-3">
                        Restore Domains
                    </h1>
                    <p className="text-md text-gray-500">
                        Handles restoration of previously expired domains.
                    </p>
                </div>

                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>

                <div className="items-center">
                    <div className="flex items-center space-x-2">
                        <label className="flex items-center">
                            <MdOutlineFilterAlt className="text-gray-500" />
                            <span className="ml-2 text-sm text-gray-600">
                                Filter:
                            </span>
                        </label>
                        <Filter />
                    </div>
                </div>

                {hasSpinner ? (
                    <div className="mx-auto container mt-16 flex flex-col px-28 rounded-lg">
                        <LoaderSpinner
                            ml="ml-72"
                            h="h-12"
                            w="w-12"
                            position="absolute"
                        />
                        <br />
                        <span className="relative top-9 left-20 ml-44">
                            Loading Data...
                        </span>
                    </div>
                ) : hasItem ? (
                    <div>
                        <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg overflow-hidden">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium uppercase">
                                        Domain
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium uppercase">
                                        Total Amount
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium uppercase">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium uppercase">
                                        Valid Date
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium uppercase"></th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200 text-sm">
                                {items.map((item) => (
                                    <Item
                                        key={`domainRedemption-${item.id}`}
                                        item={item}
                                    />
                                ))}
                            </tbody>
                        </table>
                    </div>
                ) : (
                    <DomainNotFound />
                )}
            </div>
        </UserLayout>
    );
}
