<?php

namespace App\Modules\DomainRedemption\Services\JobServices;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\DomainRedemption\Services\JobServices\JobRedemptionRecord;
use App\Modules\DomainRedemption\Services\JobServices\JobRedemptionDispatchService;
use App\Modules\DomainRedemption\Services\Payloads\RestoreReportPayload;
use App\Util\Constant\QueueErrorTypes;
use Exception;

class JobRedemptionEppService
{
    use UserLoggerTrait;

    private JobRedemptionRecord $jobRecord;

    public static function instance(): self
    {
        return new self;
    }

    public function handle(JobRedemptionRecord $record): void
    {
        $this->jobRecord = $record;

        app(AuthLogger::class)->info($this->fromWho("Domain redemption started: {$this->jobRecord->domainName}", $this->jobRecord->email));

        $this->dispatchEppJobs();
    }

    private function dispatchEppJobs(): void
    {
        $restoreResponse = $this->callEppDomainRestoreRequest();
        $this->evaluateResponse($restoreResponse, 'Restore Request');

        $eppInfoResponse = $this->callEppInfo();
        $this->evaluateResponse($eppInfoResponse, 'EPP Info');

        $reportResponse = $this->callEppDomainRestoreReport(eppInfoData: $eppInfoResponse);
        $this->evaluateResponse($reportResponse, 'Restore Report');

        $this->dispatchSuccessJob();
    }


    private function dispatchSuccessJob(): void
    {
        $domain = (object) [
            'domain_id' => $this->jobRecord->domainId,
            'domain_name' => $this->jobRecord->domainName
        ];

        JobRedemptionDispatchService::instance()->redemptionSuccessDispatch($domain, $this->jobRecord->userId, $this->jobRecord->refundDetails);
    }

    private function callEppDomainRestoreRequest(): array
    {
        return EppDomainService::instance()->callEppDomainRestoreRequest($this->jobRecord->domainName);
    }

    private function callEppInfo(): array
    {
        // app(AuthLogger::class)->info($this->fromWho("EPP info called!"));
        return EppDomainService::instance()->callEppInfo($this->jobRecord->domainName);
    }

    private function callEppDomainRestoreReport(array $eppInfoData): array
    {
        $payload = new RestoreReportPayload($this->jobRecord->domainName, $this->jobRecord->email, $eppInfoData);
        return EppDomainService::instance()->callEppDomainRestoreReport($payload->toArray());
    }

    private function evaluateResponse(array $response, string $operation): void
    {
        if (!$this->isEppResponseSuccessful($response)) {
            $errorDetails = $this->extractErrorDetails($response);
            $errorMessage = "{$operation} failed: {$errorDetails}";
            app(AuthLogger::class)->error($this->fromWho($errorMessage, $this->jobRecord->email));

            if ($this->isRetryableError($response)) {
                throw new Exception(QueueErrorTypes::RETRY);
            }

            // For non-retryable EPP errors, process refund immediately
            if (isset($response['eppCode'])) {
                app(AuthLogger::class)->info($this->fromWho("Non-retryable EPP error {$response['eppCode']}, processing refund immediately", $this->jobRecord->email));
                $this->jobRecord->processRefund("EPP Error {$response['eppCode']}: {$errorDetails}");
            }

            throw new Exception($errorMessage);
        }
    }

    private function isRetryableError(array $response): bool
    {
        if (isset($response['eppCode'])) {
            $retryableCodes = [
                '2400',
                '2500',
                '2502',
            ];

            return in_array($response['eppCode'], $retryableCodes);
        }

        return true;
    }

    private function extractErrorDetails(array $response): string
    {
        if (isset($response['status']['message'])) {
            $message = $response['status']['message'];
            $eppCode = $response['status']['eppCode'] ?? 'Unknown';
            return "EPP Error {$eppCode}: {$message}";
        }

        if (isset($response['errors'])) {
            return is_array($response['errors']) ? implode(', ', $response['errors']) : $response['errors'];
        }

        return json_encode($response);
    }



    private function isEppResponseSuccessful(array $response): bool
    {
        if (isset($response['status'])) {
            if ($response['status'] === 'OK' || $response['status'] === 'success') {
                return true;
            }

            if (is_array($response['status'])) {
                $statusCode = $response['status']['statusCode'] ?? null;
                $status = $response['status']['status'] ?? null;

                if (in_array($statusCode, [200, 1000, 1001])) {
                    return true;
                }

                if (in_array($status, ['FORBIDDEN', 'ERROR', 'FAILED'])) {
                    return false;
                }
            }
        }

        if (isset($response['statusCode']) && in_array($response['statusCode'], [200, 1000, 1001])) {
            return true;
        }

        if (!isset($response['errors']) && !isset($response['error']) && !isset($response['status']['message'])) {
            return true;
        }

        return false;
    }




}
