<?php

namespace App\Modules\Notification\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Notification\Constants\NotificationType;
use App\Util\Constant\TableTypes;

class DomainNotificationService extends NotificationService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $domainNotificationService = new self;

        return $domainNotificationService;
    }

    public function sendBulkDomainInProcessStatusNotif(array $domains): void
    {
        $notification = NotificationHandler::For(auth()->id());

        foreach ($domains as $domain) {
            $message = 'The payment invoice for the registration of the domain "'.strtoupper($domain).'" is now being processed.';
            $notification->addPayload('Domain is In Process', $message, 'domain', NotificationType::LOW);
        }

        $notification->store();
        app(AuthLogger::class)->info($this->fromWho('Domains on process: '.implode(',', $domains)));
    }

    public function sendDomainFailedStatusNotif(string $domain, string $userId): void
    {
        $message = 'Sorry, the registration for the domain "'.strtoupper($domain).'" was unsuccessful. A refund is in process. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Registration Failed', $message, 'domain', NotificationType::CRITICAL);
        $notification->store()->notifySelf()->updatePages(TableTypes::DOMAIN);
    }

    public function sendDomainAlreadyRegistered(string $domain, string $userId): void
    {
        $message = 'Sorry, the registration for the domain "'.strtoupper($domain).'" was unsuccessful. A refund is in process. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Already Exists', $message, 'domain', NotificationType::IMPORTANT);
        $notification->store()->notifySelf();
    }

    public function sendDomainRefund(string $domain, string $userId): void
    {
        $message = 'Refund for domain "'.strtoupper($domain).'" was requested. Click to view payment summary.';
        $notification = NotificationHandler::For($userId)->addPayload('Refund Request Initiated', $message, 'payment.summary', NotificationType::MEDIUM);
        $notification->store();
    }

    public function sendDomainRefundFailed(string $domain, string $userId): void
    {
        $message = 'Refund request for domain "'.strtoupper($domain).'" has failed. Please contact administrator.';
        $notification = NotificationHandler::For($userId)->addPayload('Refund Request Failed', $message, 'payment.summary', NotificationType::CRITICAL);
        $notification->store();
    }

    public function sendBulkDomainFailedStatusNotif(array $domains): void
    {
        $notification = NotificationHandler::For(auth()->id());

        foreach ($domains as $domain) {
            $message = 'Sorry, the registration for the domain "'.strtoupper($domain).'" was unsuccessful. Click to view your domains.';
            $notification->addPayload('Domain Registration Failed', $message, 'domain', NotificationType::CRITICAL);
        }

        $notification->notifySelf();
        app(AuthLogger::class)->info($this->fromWho('Failed to register domains: '.implode(',', $domains)));
    }

    public function sendDomainActiveStatusNotif(string $domain, string $userId): void
    {
        $message = 'Congratulations! The registration for the domain "'.strtoupper($domain).'" was successful and is now ACTIVE. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Registration Success', $message, 'domain', NotificationType::MEDIUM);
        $notification->store()->notifySelf()->updatePages(TableTypes::DOMAIN);
        app(AuthLogger::class)->info($this->fromWho('Notification successfully created '.$domain.' domain and is now active.'));
    }

    public function sendAuthCodeRequestInProcessNotif(): void
    {
        $message = 'Your request is currently being processed. Kindly await an email that will include the attached CSV file containing the authentication code/s.';
        $notification = NotificationHandler::For(auth()->id())->addPayload('Auth Code is In Process', $message, 'domain', NotificationType::IMPORTANT);
        $notification->store();
    }

    public function sendAuthCodeRequestCompleteNotif(string $userId): void
    {
        $message = 'Your request has been successfully processed. Please check your inbox.';
        $notification = NotificationHandler::For($userId)->addPayload('Auth Code was Sent', $message, 'domain', NotificationType::MEDIUM);
        $notification->store()->notifySelf();
    }

    public function sendBulkDomainActiveStatusNotif(array $domains): void
    {
        $notification = NotificationHandler::For(auth()->id());

        foreach ($domains as $domain) {
            $message = 'Congratulations! The registration for the domain "'.strtoupper($domain).'" was successful and is now ACTIVE. Click to view your domains.';
            $notification->addPayload('Domain Registration Success', $message, 'domain', NotificationType::MEDIUM);
        }

        $notification->store();
        app(AuthLogger::class)->info($this->fromWho('Successfully created '.implode(',', $domains).' domains and is now active.'));
    }

    public function sendDomainRenewalNotif(string $domain, string $userId): void
    {
        $message = 'The expiration of the domain "'.strtoupper($domain).'" has been updated. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Renewal Success', $message, 'domain', NotificationType::MEDIUM);
        $notification->store()->notifySelf()->updatePages(TableTypes::DOMAIN);
        app(AuthLogger::class)->info($this->fromWho('updated the expiration of the domain '.$domain.'.'));
    }

    public function sendBulkDomainRenewalNotif(array $domains): void
    {
        $notification = NotificationHandler::For(auth()->id());

        foreach ($domains as $domain) {
            $message = 'The expiration of the domain "'.strtoupper($domain).'" has been updated. Click to view your domains.';
            $notification->addPayload('Domain Renewal Success', $message, 'domain', NotificationType::MEDIUM);
        }

        $notification->store();
        app(AuthLogger::class)->info($this->fromWho('updated the expiration of the domain '.$domain.'.'));
    }

    public function sendDomainRenewalFailedNotif(string $domain, string $userId): void
    {
        $message = 'Sorry, the renewal for the domain "'.strtoupper($domain).'" was unsuccessful. A refund is in process. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Renewal Failed', $message, 'domain', NotificationType::CRITICAL);
        $notification->store()->notifySelf();
    }

    public function sendBulkDomainRenewalFailedNotif(array $domains): void
    {
        $notification = NotificationHandler::For(auth()->id());

        foreach ($domains as $domain) {
            $message = 'Sorry, the renewal for the domain "'.strtoupper($domain).'" was unsuccessful. Click to view your domains.';
            $notification->addPayload('Domain Renewal Failed', $message, 'domain', NotificationType::CRITICAL);
        }

        $notification->store();
        app(AuthLogger::class)->info($this->fromWho('Failed to renew domains: '.implode(',', $domains)));
    }

    public function sendDomainUpdateNotif(string $domain, string $userId): void
    {
        $message = 'The domain "'.strtoupper($domain).'" has been updated. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Update Success', $message, 'domain', NotificationType::MEDIUM);
        $notification->store()->notifySelf()->updatePages(TableTypes::DOMAIN);
    }

    public function sendDomainUpdateFailedNotif(string $domain, string $userId): void
    {
        $message = 'Sorry, the update for the domain "'.strtoupper($domain).'" was unsuccessful. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Update Failed', $message, 'domain', NotificationType::CRITICAL);
        $notification->store()->notifySelf()->updatePages(TableTypes::DOMAIN);
    }

    public function sendDomainDeletionNotif(string $domain, string $userId): void
    {
        $message = 'Your domain "'.strtoupper($domain).'" has been expired for over 40 days and is scheduled for deletion soon. Please contact the support immediately to explore your options.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Redemption Notice', $message, 'domain', NotificationType::IMPORTANT);
        $notification->store()->notifySelf();
    }
    
    public function sendDomainRedemptionFailedNotif(string $domain, string $userId): void
    {
        $message = 'Sorry, the restore for the domain "'.strtoupper($domain).'" was unsuccessful. A refund is in process. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Restore Failed', $message, 'domain', NotificationType::CRITICAL);
        $notification->store()->notifySelf();
    }

    public function sendDomainRedemptionNotif(string $domain, string $userId): void
    {
        $message = 'The restoration request for your domain "' . strtoupper($domain) . '" has been received and is currently awaiting admin approval. You will be notified once it is processed. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Restore Success', $message, 'domain', NotificationType::MEDIUM);
        $notification->store()->notifySelf()->updatePages(TableTypes::DOMAIN);
    }

    public function sendDomainRedemptionSuccessNotif(string $domain, string $userId): void
    {
        $message = 'The restoration for your domain "' . strtoupper($domain) . '" has been successfully completed and is now fully restored. Click to view your domains.';
        $notification = NotificationHandler::For($userId)->addPayload('Domain Restore Completed', $message, 'domain', NotificationType::MEDIUM);
        $notification->store()->notifySelf()->updatePages(TableTypes::DOMAIN);
    }
}
