<?php if (isset($component)) { $__componentOriginalaa758e6a82983efcbf593f765e026bd9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa758e6a82983efcbf593f765e026bd9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => $__env->getContainer()->make(Illuminate\View\Factory::class)->make('mail::message'),'data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mail::message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
## <?php echo new \Illuminate\Support\EncodedHtmlString($greeting); ?>


<p>
    Unfortunately, we were unable to verify your bank transfer request. 
</p>

<?php $__env->startComponent('mail::table'); ?>
| |
| :--------- |
| **Date:** <?php echo new \Illuminate\Support\EncodedHtmlString(\Carbon\Carbon::parse($bankTransfer->created_at)->format('F j, Y h:i:s A')); ?> |
| **Pending Amount:** $ <?php echo new \Illuminate\Support\EncodedHtmlString($bankTransfer->gross_amount ?? '0.00'); ?> |
<?php echo $__env->renderComponent(); ?>

<p>
    Please try a new request or select another payment method to add funds to your account by visiting <i><a href="<?php echo new \Illuminate\Support\EncodedHtmlString($redirectUrl); ?>">this link</a></i>.
</p>

<p>
    If you have any questions or need assistance, feel free to contact us at <i><a href="<?php echo new \Illuminate\Support\EncodedHtmlString($supportUrl); ?>">StrangeDomains.com</a></i>.
    Or call us at <strong><?php echo new \Illuminate\Support\EncodedHtmlString($phoneNumber); ?></strong>.
</p>

## Sincerely,
## <?php echo new \Illuminate\Support\EncodedHtmlString($senderName); ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $attributes = $__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $component = $__componentOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?><?php /**PATH C:\1xampp\htdocs\sd-client\resources\views/Mails/BankTransferMail/Rejected.blade.php ENDPATH**/ ?>