<?php if (isset($component)) { $__componentOriginalaa758e6a82983efcbf593f765e026bd9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa758e6a82983efcbf593f765e026bd9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => $__env->getContainer()->make(Illuminate\View\Factory::class)->make('mail::message'),'data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mail::message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
## <?php echo new \Illuminate\Support\EncodedHtmlString($greeting); ?>


<p><?php echo new \Illuminate\Support\EncodedHtmlString($body); ?></p>
<p><?php echo new \Illuminate\Support\EncodedHtmlString($text); ?></p>
<p><a href="<?php echo new \Illuminate\Support\EncodedHtmlString($refundUrl); ?>">View Refund Details</a><br></p>

<?php $__env->startComponent('mail::table'); ?>
| SUMMARY | 
| :--------- |
| Invoice #: <?php echo new \Illuminate\Support\EncodedHtmlString($data['invoice_num']); ?>| 
| Date: <?php echo new \Illuminate\Support\EncodedHtmlString($data['date']); ?> | 
<?php echo $__env->renderComponent(); ?>

<?php $__env->startComponent('mail::table'); ?>
| Domain       | Type           |Refunded Amount       |
| :---------   | :------------- |:----------- |
| <?php echo new \Illuminate\Support\EncodedHtmlString($domain); ?> | <?php echo new \Illuminate\Support\EncodedHtmlString($data['node_type']); ?> | $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($refunded_amount,2)); ?> |
<?php echo $__env->renderComponent(); ?>
 
For more information, please see <?php echo new \Illuminate\Support\EncodedHtmlString(config('app.name')); ?>'s <a href="<?php echo new \Illuminate\Support\EncodedHtmlString($policyUrl); ?>">Refund Policy</a>.<br>


## Sincerely,
## <?php echo new \Illuminate\Support\EncodedHtmlString($sender); ?>


 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $attributes = $__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $component = $__componentOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?><?php /**PATH C:\1xampp\htdocs\sd-client\resources\views/Mails/RefundSuccess.blade.php ENDPATH**/ ?>