<?php

namespace App\Modules\DomainRedemption\Constants;

final class RedemptionStatus
{
    public const NOT_PAID = 'not_paid';
    public const IN_PROCESS = 'in_process';
    public const COMPLETED = 'completed';
    public const ALL = 'all';

    public const VALID_STATUSES = [
        self::ALL,
        self::NOT_PAID,
        self::IN_PROCESS,
        self::COMPLETED,
    ];

    public const STATUS_LABELS = [
        self::NOT_PAID => 'Not Paid',
        self::IN_PROCESS => 'In Process',
        self::COMPLETED => 'Completed',
    ];

    public const VALID_ORDER_BY = [
        'domain_name',
        'total_amount',
        'created_at',
    ];

    public static function getValidStatuses(): array
    {
        return self::VALID_STATUSES;
    }

    public static function getStatusLabel(string $status): string
    {
        return self::STATUS_LABELS[$status] ?? ucfirst(str_replace('_', ' ', $status));
    }

    public static function getValidOrderBy(): array
    {
        return self::VALID_ORDER_BY;
    }
}
