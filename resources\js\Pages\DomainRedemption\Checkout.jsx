//* PACKAGES
import React, { useEffect, useState } from "react";
import { usePage, router } from "@inertiajs/react";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from '@stripe/react-stripe-js';
import defaultTheme from 'tailwindcss/defaultTheme';


//* COMPONENTS
import UserLayout from "@/Layouts/UserLayout";
import StripeCheckoutFormComponent from '@/Components/Stripe/StripeCheckoutFormComponent';
import CheckOutForm from '@/Components/AccountCredit/CheckOutForm';
import _PaymentSummary from '@/Constant/_PaymentSummary';
import PrimaryButton from '@/Components/PrimaryButton';
import Checkbox from '@/Components/Checkbox';
import LoaderSpinner from "@/Components/LoaderSpinner";
import StripeOrderTotal from "@/Components/Domain/Checkout/StripeOrderTotal";
import OrderTotal from "@/Components/Domain/Checkout/OrderTotal";

export default function Checkout({
    domain,
    other_fees,
    secret,
    promise,
    intent,
    account_credit_balance = 0,
    stripeFeeObj = [],
}) {
    //! PACKAGE
    const user = usePage().props.auth.user;
    const stripePromise = loadStripe(promise);
    const stripeSecret = secret;
    const fontsArray = ["Figtree", ...defaultTheme.fontFamily.sans];
    const fonts = fontsArray.join(", ");
    const appearance =
    {
        theme: 'stripe',
        variables:
        {
            fontFamily: fonts,
            colorText: '#374151',
        }
    };
    const stripeOptions =
    {
        clientSecret: stripeSecret,
        appearance: appearance,
    }

    //! STATE
    const [paymentMethod, setPaymentMethod] = useState('stripe');
    const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState(null);
    const [isAgree, setIsAgree] = useState(false);
    const [validationError, setValidationError] = useState('');
    const [isPageProcessing, setPageProcessing] = useState(false);


    //! USE EFFECTS
    useEffect(
        () => {
            window.history.pushState(null, '', window.location.href);
            window.onpopstate = () => {
                window.history.replaceState(null, '', router.get('domain-redemption'));
                window.location.reload();
            };
        },
        []
    );

    //! FUNCTIONS
    const handlePaymentMethodChange = (event) => {
        setPaymentMethod(event.target.value);
        if (event.target.value !== 'saved_card') {
            setSelectedPaymentMethodId(null);
        }
    };

    const handlePaymentMethodSelect = (methodId) => {
        setSelectedPaymentMethodId(methodId);
        setPaymentMethod('saved_card');
    };

    const handleAgreementChange = (event) => {
        setIsAgree(event.target.checked);
    };

    const getRedemptionFee = () => {
        return other_fees.redemption_fee
    }

    const getICANNFee = () => {
        return other_fees.icann_fee
    }





    const getGrossAmount = (paymentMethod) => {
        switch (paymentMethod) {
            case 'saved_card':
                return stripeFeeObj.gross_amount ?? other_fees.bill_total;
            case 'stripe':
                return stripeFeeObj.gross_amount ?? other_fees.bill_total;
            case 'account_credit':
                return other_fees.bill_total;
            default:
                return other_fees.bill_total;
        }
    }

    return (
        <UserLayout hideNav={true} postRouteName={'domain-redemption.pay'}>
            {isPageProcessing && <>
                <div className={`
                                fixed top-0 left-0 w-full min-h-screen bg-gray-500/50 z-50
                                flex flex-col justify-around
                                items-center
                                px-10 pt-5 pb-5
                                gap-y-5
                            `}>
                    <LoaderSpinner />
                </div>
            </>}
            <div className="mx-auto container max-w-6xl py-12">
                <div className="grid grid-cols-12 gap-5">
                    <div className="col-span-7">
                        <div className="flex flex-col">
                            <div className="mb-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">
                                    Select payment method
                                </h2>
                                <div className="border-b border-gray-200 mb-4"></div>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center space-x-4">
                                    <div
                                        className={`flex-grow cursor-pointer transition-all duration-200 ${paymentMethod === 'saved_card' ? '' : 'opacity-50'}`}
                                        onClick={() => {
                                            const radioBtn = document.querySelector('input[type="radio"][value="saved_card"]');
                                            if (radioBtn) {
                                                radioBtn.click();
                                            }
                                            setPaymentMethod('saved_card');
                                        }}
                                    >
                                        <Elements
                                            stripe={stripePromise}
                                            options={stripeOptions}
                                        >
                                            <StripeCheckoutFormComponent
                                                type={'redemption'}
                                                user_id={user.id}
                                                domains={[domain]}
                                                other_fees={other_fees}
                                                intent={intent}
                                                paymentMethod={'saved_card'}
                                                isActive={paymentMethod === 'saved_card'}
                                                selectedPaymentMethodId={selectedPaymentMethodId}
                                                onPaymentMethodSelect={handlePaymentMethodSelect}
                                                stripeFeeObj={stripeFeeObj}
                                                onHandlePageProcessing={setPageProcessing}
                                            />
                                        </Elements>
                                    </div>
                                    {/* <div className="ml-4">
                                        <input
                                            type="radio"
                                            name="payment_method"
                                            value="saved_card"
                                            checked={paymentMethod === 'saved_card'}
                                            onChange={handlePaymentMethodChange}
                                            className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                        />
                                    </div> */}
                                </div>

                                <div className="flex items-center space-x-4">
                                    <div
                                        className={`flex-grow cursor-pointer transition-all duration-200 ${paymentMethod === 'stripe' ? '' : 'opacity-50'}`}
                                        onClick={() => {
                                            const radioBtn = document.querySelector('input[type="radio"][value="stripe"]');
                                            if (radioBtn) {
                                                radioBtn.click();
                                            }
                                            setPaymentMethod('stripe');
                                        }}
                                    >
                                        <Elements
                                            stripe={stripePromise}
                                            options={stripeOptions}
                                        >
                                            <StripeCheckoutFormComponent
                                                type={'redemption'}
                                                user_id={user.id}
                                                domains={[domain]}
                                                other_fees={other_fees}
                                                intent={intent}
                                                paymentMethod={'stripe'}
                                                isActive={paymentMethod === 'stripe'}
                                                stripeFeeObj={stripeFeeObj}
                                                onHandlePageProcessing={setPageProcessing}
                                            />
                                        </Elements>
                                    </div>
                                    <div className="ml-4">
                                        <input
                                            type="radio"
                                            name="payment_method"
                                            value="stripe"
                                            checked={paymentMethod === 'stripe'}
                                            onChange={handlePaymentMethodChange}
                                            className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                        />
                                    </div>
                                </div>

                                {account_credit_balance >= other_fees.bill_total && (
                                    <div className="flex items-center space-x-4">
                                        <div
                                            className={`flex-grow cursor-pointer transition-all duration-200 ${paymentMethod === 'account_credit' ? '' : 'opacity-50'}`}
                                            onClick={() => {
                                                const radioBtn = document.querySelector('input[type="radio"][value="account_credit"]');
                                                if (radioBtn) {
                                                    radioBtn.click();
                                                }
                                                setPaymentMethod('account_credit');
                                            }}
                                        >
                                            <CheckOutForm
                                                user={user}
                                                domains={[domain]}
                                                other_fees={other_fees}
                                                type={'redemption'}
                                                availableCredit={account_credit_balance}
                                                isActive={paymentMethod === 'account_credit'}
                                                onHandlePageProcessing={setPageProcessing}
                                            />
                                        </div>
                                        <div className="ml-4">
                                            <input
                                                type="radio"
                                                name="payment_method"
                                                value="account_credit"
                                                checked={paymentMethod === 'account_credit'}
                                                onChange={handlePaymentMethodChange}
                                                className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>


                        </div>
                    </div>

                    <div className="col-span-5">
                        <div className="bg-gray-50 rounded-lg p-6">
                            <div className="mb-6">
                                <div className="flex items-center space-x-3">
                                    <h2 className="text-lg font-semibold text-gray-800">Order Summary</h2>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <div className="pb-4 border-b border-gray-200">
                                    <div className="flex flex-col items-left justify-between text-gray-600 mb-6">
                                        <span className="font-semibold">
                                            Domain Redemption
                                        </span>
                                        <div className='flex flex-col mt-1'>
                                            <div className='flex justify-between'>
                                                <span className=''>{domain.domain_name}</span>
                                                <span className='text-sm'>${parseFloat(getRedemptionFee()).toFixed(2)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-left justify-between text-gray-600 ">
                                        <span className="font-semibold text-[15px]"> ICANN Fee </span>
                                        <span className='text-sm'>${getICANNFee().toFixed(2)}</span>
                                    </div>
                                </div>
                                {(paymentMethod === 'saved_card' || paymentMethod === 'stripe') ?
                                    <StripeOrderTotal total={getGrossAmount(paymentMethod)} service_fee={stripeFeeObj.service_fee} /> :
                                    <OrderTotal total={getGrossAmount(paymentMethod)} />}
                                {validationError && (
                                    <div className="mb-4 text-sm text-red-600">
                                        {validationError}
                                    </div>
                                )}
                                <div className="mb-6">
                                    <label className="flex items-center">
                                        <Checkbox
                                            name="is_agree"
                                            value="is_agree"
                                            checked={isAgree}
                                            handleChange={handleAgreementChange}
                                        />
                                        <span className="ml-2 text-sm text-gray-600">
                                            I agree to the &nbsp;
                                            <a className="underline text-sm text-link" href={route("terms")} target="_blank">
                                                Terms and Conditions
                                            </a>
                                            &nbsp; of StrangeDomains.
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <PrimaryButton
                                        type='submit'
                                        className={(!isAgree ? "!bg-[#CBD5E1]" : "") + " w-full"}
                                        processing={!isAgree || isPageProcessing}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            setValidationError('');

                                            if (!paymentMethod) {
                                                setValidationError('Please select a payment method to continue.');
                                                return;
                                            }

                                            if (paymentMethod === 'saved_card' && !selectedPaymentMethodId) {
                                                setValidationError('Please select a saved card or choose a different payment method.');
                                                return;
                                            }

                                            if (isAgree) {
                                                let activeForm;
                                                if (paymentMethod === 'saved_card' || paymentMethod === 'stripe') {
                                                    const forms = document.querySelectorAll('form');
                                                    forms.forEach(form => {
                                                        if (!form.classList.contains('opacity-50')) {
                                                            activeForm = form;
                                                        }
                                                    });
                                                } else if (paymentMethod === 'account_credit') {
                                                    activeForm = document.querySelector('form[data-payment-type="account_credit"]');
                                                }

                                                if (activeForm) {
                                                    activeForm.requestSubmit();
                                                }
                                            }
                                        }}
                                        enableHover={false}
                                    >
                                        <div className={(isAgree ? "text-white" : "text-black") + " w-full flex items-center justify-between"}>
                                            <span>Confirm Payment</span>
                                            <div className='flex flex-wrap items-center justify-center'>
                                                <span>
                                                    ${Number(parseFloat(getGrossAmount(paymentMethod)).toFixed(2)).toLocaleString('en', { useGrouping: true, minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                                </span>
                                                <span className={"bg-black/40 text-[0.65rem] py-1 px-2 rounded-md ml-2"}>
                                                    1 item
                                                </span>
                                            </div>
                                        </div>
                                    </PrimaryButton>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </UserLayout>
    );
}
