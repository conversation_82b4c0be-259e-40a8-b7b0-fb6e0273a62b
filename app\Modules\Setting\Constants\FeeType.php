<?php

namespace App\Modules\Setting\Constants;

use App\Modules\Epp\Constants\RegistryTransactionType;
use Illuminate\Support\Str;

final class FeeType
{
    public const REGISTRATION = 'REGISTRATION';

    public const TRANSFER = 'TRANSFER';

    public const RENEW = 'RENEW';

    public const REDEMPTION = 'REDEMPTION';

    public const PROTECTION = 'PROTECTION';

    public const PENALTY_LATE_RENEWAL = 'PENALTY_LATE_RENEWAL';

    public const FEE_TYPE = [
        self::REGISTRATION => 'register_fee',
        self::RENEW => 'renewal_fee',
        self::TRANSFER => 'transfer_fee',
        self::REDEMPTION => 'redemption_fee',
    ];

    public const TRANSACTION_TYPE = [
        self::REGISTRATION => RegistryTransactionType::DOMAIN_REGISTRATION,
        self::TRANSFER => RegistryTransactionType::DOMAIN_TRANSFER,
        self::RENEW => RegistryTransactionType::DOMAIN_RENEWAL,
        self::REDEMPTION => RegistryTransactionType::DOMAIN_RENEWAL,
    ];

    public static function all()
    {
        return [self::REGISTRATION, self::TRANSFER, self::RENEW, self::REDEMPTION, self::PROTECTION, self::PENALTY_LATE_RENEWAL];
    }

    public static function getText($type)
    {
        if ($type == self::RENEW) {
            return 'RENEWAL';
        } else {
            return $type;
        }
    }

    public static function getName($type)
    {
        if ($type == self::RENEW) {
            return 'Renewal';
        } else {
            return Str::ucfirst(strtolower($type));
        }
    }
}
