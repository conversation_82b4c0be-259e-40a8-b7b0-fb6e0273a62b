[2025-08-01 03:26:49] local.INFO: mjb<PERSON><PERSON><PERSON>@gmail.com Created stripe intent: usd 10510  
[2025-08-01 03:26:58] local.INFO: mjb<PERSON><EMAIL> Created payment invoice with value 100.18,100.18,PAID,1,29  
[2025-08-01 03:26:58] local.INFO: mjb<PERSON>lo<PERSON>@gmail.com Created payment invoice with value {"total_amount":100.18,"paid_amount":100.18,"status":"PAID","total_payment_node":1,"payment_service_id":29,"updated_at":"2025-08-01T03:26:58.000000Z","created_at":"2025-08-01T03:26:58.000000Z","id":21}  
[2025-08-01 03:26:58] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-01 03:26:58] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-01 03:26:58] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-01 03:26:58] local.INFO: <EMAIL> Redemption payment service created: Order 15, Payment Service 29  
[2025-08-01 03:27:00] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-01 03:27:00] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-01 03:27:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-01 03:27:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:27:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:27:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:27:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:27:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:27:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:27:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:27:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:27:59] local.INFO: <EMAIL> Domain redemption started: pwdforyou.net  
[2025-08-01 03:28:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:28:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:28:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:28:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:28:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:28:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:28:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:28:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:28:02] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:28:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:28:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:28:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:28:04] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:28:04] local.INFO: AccountCreditor: Done  
[2025-08-01 03:28:05] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:28:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:28:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:28:06] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:29:00] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:29:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:29:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:29:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:29:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:29:01] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:29:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:29:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:29:04] local.INFO: <EMAIL> Domain redemption success processing started: pwdforyou.net  
[2025-08-01 03:29:08] local.INFO: <EMAIL> Domain restored from deletion queue: pwdforyou.net  
[2025-08-01 03:29:08] local.INFO: <EMAIL> Updated domain id 97 to status ACTIVE  
[2025-08-01 03:29:08] local.INFO: <EMAIL> Domain status updated to ACTIVE: pwdforyou.net  
[2025-08-01 03:29:08] local.INFO: <EMAIL> Update payment nodes with registered domain id 13 column status with value COMPLETED  
[2025-08-01 03:29:08] local.INFO: <EMAIL> Payment node marked as completed: pwdforyou.net  
[2025-08-01 03:29:08] local.INFO: <EMAIL> Redemption payment service completed: Order 15, Payment Service 29  
[2025-08-01 03:29:08] local.INFO: Guest User Payment summary found for redemption: pwdforyou.net, Summary ID: 19  
[2025-08-01 03:29:09] local.INFO: <EMAIL> Domain table updated for user: 7  
[2025-08-01 03:29:09] local.INFO: Domain History: Domain 'pwdforyou.net' redemption completed <NAME_EMAIL>. Domain restored to ACTIVE status.  
[2025-08-01 03:29:09] local.INFO: <EMAIL> Domain redemption completed: pwdforyou.net  
[2025-08-01 03:30:03] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-01 03:30:04] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:30:04] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:30:05] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-01 03:30:05] local.INFO: PostAutoRenewalGracePeriodHandler: No domains found...  
[2025-08-01 03:30:05] local.INFO: PostAutoRenewalGracePeriodHandler: Done  
[2025-08-01 03:30:06] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-01 03:30:06] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-01 03:30:06] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-01 03:30:06] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:30:06] local.INFO: GeneralNotification: done  
[2025-08-01 03:30:07] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:30:07] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:30:07] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:30:07] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:30:08] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:30:08] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:30:09] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:30:09] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:30:10] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:30:10] local.INFO: AccountCreditor: Done  
[2025-08-01 03:30:11] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:30:12] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:30:12] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:30:12] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:31:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:31:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:31:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:31:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:31:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:31:01] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:31:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:31:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:31:23] local.INFO: <EMAIL> Created stripe intent: usd 50000  
[2025-08-01 03:31:30] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3Rr9gmAYkyIIc8ES2eLENZvz","object":"payment_intent","amount":50000,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":50000,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3Rr9gmAYkyIIc8ES2eLENZvz_secret_LdPyIpbzgDCw6gXnbfDRGnpCc","confirmation_method":"automatic","created":1754019084,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3Rr9gmAYkyIIc8ES2MfNK4Po","object":"charge","amount":50000,"amount_captured":50000,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3Rr9gmAYkyIIc8ES2PNGnulA","object":"balance_transaction","amount":50000,"available_on":1754352000,"balance_type":"payments","created":**********,"currency":"usd","description":null,"exchange_rate":null,"fee":1480,"fee_details":[{"amount":1480,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":48520,"reporting_category":"charge","source":"ch_3Rr9gmAYkyIIc8ES2MfNK4Po","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":"Miake","phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1754019084,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":7,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3Rr9gmAYkyIIc8ES2eLENZvz","payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_details":{"card":{"amount_authorized":50000,"authorization_code":"130755","brand":"visa","capture_before":1754623884,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":null},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":50000,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKJLqsMQGMgYlIXO8R6Y6LBaO7cHaTyG9kMgQ1KJbnOeyf42UatIIxdLFyx_MnchlqChsLQNsClfQcsBL","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-08-01 03:31:30] local.INFO: getStripeIntentById balance:: {"id":"txn_3Rr9gmAYkyIIc8ES2PNGnulA","object":"balance_transaction","amount":50000,"available_on":1754352000,"balance_type":"payments","created":**********,"currency":"usd","description":null,"exchange_rate":null,"fee":1480,"fee_details":[{"amount":1480,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":48520,"reporting_category":"charge","source":"ch_3Rr9gmAYkyIIc8ES2MfNK4Po","status":"pending","type":"charge"}  
[2025-08-01 03:31:30] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"user_id":7,"gross_amount":500,"net_amount":485.2,"bill_amount":null,"service_fee":14.8,"payment_intent":"pi_3Rr9gmAYkyIIc8ES2eLENZvz","adjustments":null,"charge_id":"ch_3Rr9gmAYkyIIc8ES2MfNK4Po"}  
[2025-08-01 03:31:30] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":500,"net_amount":485.2,"service_fee":14.8,"charge_id":"ch_3Rr9gmAYkyIIc8ES2MfNK4Po","payment_intent":"pi_3Rr9gmAYkyIIc8ES2eLENZvz"}  
[2025-08-01 03:31:30] local.INFO: Payment summary created: Account Balance  
[2025-08-01 03:31:30] local.INFO: AccountDebitPaymentService paymentServiceObj: {"id":31,"user_id":7,"stripe_id":17,"account_credit_id":10,"bank_transfer_id":null,"system_credit_id":null,"created_at":"2025-08-01 03:31:30","updated_at":"2025-08-01 03:31:30","gross_amount":"500.00","net_amount":"485.20","service_fee":"14.80","adjustments":null,"bill_amount":null,"payment_intent":"eyJpdiI6IkZpMVFYd0dBVElGVE5ITEFKMGZCdWc9PSIsInZhbHVlIjoiMFkzQnJUa21CVTR5NjBVb2xsK3FadFR4MGNBaGJxbmlwNW5lMmNVWk96QT0iLCJtYWMiOiI5Mzk4Y2Q0NjcwYTVhMDQwYmViOTg3NTFiMGJhM2FlOTBmNTk5NzVmYjFmODg0ZjkxYjUyOTBhYzM2NjZiNDViIiwidGFnIjoiIn0=","refund_id":null,"charge_id":"eyJpdiI6IkhlM2dvTnF1QS9ndFNnb0lwKzFVZnc9PSIsInZhbHVlIjoielRaWjV3WGhYZWZrUHp3VlN6VlpQUDI2RXMvY1UrTVZpcks3NFc3QTNFWT0iLCJtYWMiOiI5YjcwMGJkODNjNjE2MDA2MGE1MDk4OWY3YTJjNGNhOTc5Y2E2YzZjOTA3NzZjYjdmYjM4ODQ1ZGE4ZGY0ZDYyIiwidGFnIjoiIn0=","paid_amount":"500.00","total_amount":"485.20","summary_name":"Account Balance","summary_type":"ACCOUNT_BALANCE","transaction_id":"eyJpdiI6Imxyak1DS1I4eTBWZDc1TWprdERBVUE9PSIsInZhbHVlIjoiTGkxaUZTS3VDbjhMUVpUNnk3dVJKMFV6RXFXWm91VlQ5S1ZYbmtZUFgzaz0iLCJtYWMiOiI4NDU1ZWEzMTZhZDJmYzE1ZWIxZjNkNGU5ODA0NDA1YzU0ZjRiNWI0NWM5NzdmYjhlYmQ0ZTEzOTk3OWNlNWYzIiwidGFnIjoiIn0=","payment_service_type":"STRIPE"}  
[2025-08-01 03:31:30] local.INFO: AccountDebitPaymentService: latestBlock {"id":10,"user_id":7,"block_index":9,"payment_service_id":null,"type":"DEBIT","running_balance":"519.32","amount":"485.2","previous_hash":"9daf4b36f4d5992317d4b82f64017671baacddf6d8d3075ffd16997dd954644a","hash":"2f34cc259cb0d3f21e62b967fd1a30b3c8ebc3095e50d545d6f3828130ecf26c","created_at":**********}  
[2025-08-01 03:31:34] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route account-balance\/store\/success. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/account-balance\/store\/success","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-01 03:31:42] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-01 03:31:42] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-01 03:31:42] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-01 03:31:42] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-01 03:31:42] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-01 03:31:42] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-08-01 03:31:53] local.INFO: <EMAIL> Created stripe intent: usd 5280  
[2025-08-01 03:31:58] local.INFO: <EMAIL> Created payment invoice with value 50.18,50.18,PAID,1,32  
[2025-08-01 03:31:58] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":50.18,"paid_amount":50.18,"status":"PAID","total_payment_node":1,"payment_service_id":32,"updated_at":"2025-08-01T03:31:58.000000Z","created_at":"2025-08-01T03:31:58.000000Z","id":22}  
[2025-08-01 03:31:58] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-01 03:31:58] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-01 03:31:58] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-01 03:31:58] local.INFO: <EMAIL> Redemption payment service created: Order 18, Payment Service 32  
[2025-08-01 03:32:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:32:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:32:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:32:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:32:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:32:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:32:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:32:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:32:04] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:32:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:32:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:32:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:32:05] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:32:05] local.INFO: AccountCreditor: Done  
[2025-08-01 03:32:06] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:32:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:32:09] local.INFO: SessionPollChecker: Updating poll list...  
[2025-08-01 03:32:09] local.INFO: AfternicUpdateTransferFromPoll: Starting ...  
[2025-08-01 03:32:09] local.INFO: AfternicUpdateTransferFromPoll: Terminating ...  
[2025-08-01 03:32:09] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:32:58] local.INFO: <EMAIL> Domain redemption started: biogenesis.net  
[2025-08-01 03:32:59] local.ERROR: 403 : Object status prohibits operation  
[2025-08-01 03:33:00] local.ERROR: 403 : Object status prohibits operation  
[2025-08-01 03:33:00] local.ERROR: <EMAIL> Restore Report failed: EPP Error 2304: Object status prohibits operation  
[2025-08-01 03:33:00] local.ERROR: <EMAIL> Retry  
[2025-08-01 03:33:00] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-01 03:33:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-01 03:33:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-01 03:33:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:33:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:33:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:33:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:33:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:33:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:33:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:33:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:34:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:34:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:34:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:34:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:34:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:34:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:34:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:34:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:34:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:34:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:34:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:34:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:34:04] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:34:04] local.INFO: AccountCreditor: Done  
[2025-08-01 03:34:05] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:34:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:34:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:34:06] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:35:00] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:35:00] local.INFO: GeneralNotification: done  
[2025-08-01 03:35:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:35:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:35:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:35:01] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:35:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:35:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:36:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:36:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:36:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-01 03:36:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-01 03:36:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-01 03:36:02] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:36:02] local.INFO: GeneralNotification: done  
[2025-08-01 03:36:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:36:03] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:36:03] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:36:03] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:36:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:36:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:36:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:36:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:36:04] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:36:05] local.INFO: AccountCreditor: Done  
[2025-08-01 03:36:06] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:36:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:36:08] local.INFO: SessionPollChecker: Updating poll list...  
[2025-08-01 03:36:08] local.INFO: AfternicUpdateTransferFromPoll: Starting ...  
[2025-08-01 03:36:08] local.INFO: AfternicUpdateTransferFromPoll: Terminating ...  
[2025-08-01 03:36:08] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:37:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:37:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:37:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:37:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:37:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:37:01] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:37:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:37:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:38:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:38:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:38:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:38:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:38:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:38:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:38:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:38:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:38:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:38:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:38:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:38:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:38:04] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:38:04] local.INFO: AccountCreditor: Done  
[2025-08-01 03:38:06] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:38:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:38:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:38:07] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:40:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:40:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:40:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:40:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:40:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:40:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:40:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:40:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:40:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:40:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:40:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:40:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:40:04] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:40:04] local.INFO: AccountCreditor: Done  
[2025-08-01 03:40:06] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:40:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:40:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:40:07] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:40:36] local.INFO: <EMAIL> Created stripe intent: usd 5280  
[2025-08-01 03:40:41] local.INFO: <EMAIL> Created payment invoice with value 50.18,50.18,PAID,1,33  
[2025-08-01 03:40:41] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":50.18,"paid_amount":50.18,"status":"PAID","total_payment_node":1,"payment_service_id":33,"updated_at":"2025-08-01T03:40:41.000000Z","created_at":"2025-08-01T03:40:41.000000Z","id":23}  
[2025-08-01 03:40:41] local.INFO: <EMAIL> Created payment nodes for  1 domains.  
[2025-08-01 03:40:41] local.INFO: <EMAIL> Created payment node invoices for  1 nodes.  
[2025-08-01 03:40:41] local.INFO: Payment summary created: Payment Invoice - Redemption  
[2025-08-01 03:40:41] local.INFO: <EMAIL> Redemption payment service created: Order 18, Payment Service 33  
[2025-08-01 03:41:00] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:41:00] local.INFO: GeneralNotification: done  
[2025-08-01 03:41:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:41:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:41:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:41:01] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:41:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:41:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:41:41] local.INFO: <EMAIL> Domain redemption started: biogenesis.net  
[2025-08-01 03:41:42] local.ERROR: 403 : Object status prohibits operation  
[2025-08-01 03:41:43] local.ERROR: 403 : Object status prohibits operation  
[2025-08-01 03:41:43] local.ERROR: <EMAIL> Restore Report failed: EPP Error 2304: Object status prohibits operation  
[2025-08-01 03:41:43] local.ERROR: <EMAIL> Retry  
[2025-08-01 03:42:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:42:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:42:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-01 03:42:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-01 03:42:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-01 03:42:02] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:42:02] local.INFO: GeneralNotification: done  
[2025-08-01 03:42:03] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:42:03] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:42:03] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:42:03] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:42:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:42:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:42:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:42:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:42:05] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:42:05] local.INFO: AccountCreditor: Done  
[2025-08-01 03:42:06] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:42:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:42:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:42:07] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:43:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:43:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:43:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:43:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:43:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:43:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:43:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:43:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:44:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:44:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:44:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:44:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:44:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:44:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:44:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:44:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:44:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:44:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:44:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:44:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:44:04] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:44:04] local.INFO: AccountCreditor: Done  
[2025-08-01 03:44:05] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:44:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:44:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:44:06] local.INFO: SessionPollChecker: Done  
[2025-08-01 03:45:02] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-01 03:45:03] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-01 03:45:03] local.INFO: PostAutoRenewalGracePeriodHandler: Skipping for 6 hours...  
[2025-08-01 03:45:04] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-01 03:45:04] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-01 03:45:04] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-01 03:45:04] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:45:05] local.INFO: GeneralNotification: done  
[2025-08-01 03:45:05] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:45:05] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:45:05] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:45:05] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:45:06] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:45:06] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:46:00] local.INFO: JobRetryScheduler: Running...  
[2025-08-01 03:46:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-01 03:46:01] local.INFO: GeneralNotification: Running...  
[2025-08-01 03:46:01] local.INFO: GeneralNotification: done  
[2025-08-01 03:46:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-01 03:46:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-01 03:46:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-01 03:46:02] local.INFO: AfternicDomainTask: done  
[2025-08-01 03:46:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-01 03:46:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-01 03:46:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-01 03:46:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-01 03:46:04] local.INFO: AccountCreditor: Running...  
[2025-08-01 03:46:04] local.INFO: AccountCreditor: Done  
[2025-08-01 03:46:05] local.INFO: SessionPollChecker: Running...  
[2025-08-01 03:46:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:46:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-01 03:46:06] local.INFO: SessionPollChecker: Done  
