<?php

namespace App\Modules\DomainRedemption\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Modules\DomainRedemption\Constants\RedemptionStatus;
use Illuminate\Validation\Rule;

class ShowListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string', 'max:255'],
            'status' => ['nullable', 'string', Rule::in(RedemptionStatus::getValidStatuses())],
            'orderby' => ['nullable', 'string', Rule::in(RedemptionStatus::getValidOrderBy())],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    public function getAllData(): array
    {
        return [
            'search' => $this->input('search'),
            'status' => $this->input('status'),
            'orderby' => $this->input('orderby'),
            'limit' => $this->input('limit', 20),
        ];
    }
}