<?php

namespace App\Modules\DomainRedemption\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => ['nullable', 'string', 'max:255'],
            'status' => ['nullable', 'string', 'in:all,not_paid,in_process,completed'],
            'orderby' => ['nullable', 'string', 'in:domain_name,total_amount,created_at'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }

    public function getAllData(): array
    {
        return [
            'search' => $this->input('search'),
            'status' => $this->input('status'),
            'orderby' => $this->input('orderby'),
            'limit' => $this->input('limit', 20),
        ];
    }
}