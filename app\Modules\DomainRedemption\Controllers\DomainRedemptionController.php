<?php

namespace App\Modules\DomainRedemption\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\DomainRedemption\Requests\ShowListRequest;
use App\Modules\DomainRedemption\Requests\RedemptionPayRequest;
use App\Modules\DomainRedemption\Requests\RedemptionCheckoutRequest;
use App\Modules\DomainRedemption\Requests\RedemptionValidationRequest;
use App\Modules\DomainRedemption\Services\DomainRedemptionService;
use Inertia\Inertia;
use Inertia\Response;

class DomainRedemptionController extends Controller
{
    public function index(ShowListRequest $request): Response
    {
        $filters = $request->getAllData();
        $service = DomainRedemptionService::instance();

        $items = $service->getAllData($filters);

        return Inertia::render('DomainRedemption/Index', [
            'items' => $items,
            'filters' => $filters
        ]);
    }

    public function show(string $id): Response
    {
        $service = DomainRedemptionService::instance();
        $domain = $service->getDomainById((int) $id);

        return Inertia::render('DomainRedemption/Show', [
            'domain' => $domain
        ]);
    }

    public function pay(RedemptionPayRequest $request): Response
    {
        return Inertia::render('DomainRedemption/Checkout', $request->getRedemptionPaymentData());
    }

    public function validateRestore(RedemptionValidationRequest $request)
    {
        $validationResult = $request->validateDomainRestore();

        return response()->json($validationResult);
    }

    public function restore(RedemptionCheckoutRequest $request): Response
    {
        $summaryId = $request->processRedemption();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment Successful. Domain redemption is in process.',
            'redirect' => [
                ['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice'],
                ['route' => route('domain-redemption'), 'label' => 'Back to Restore Domain']
            ],
        ]);
    }

}