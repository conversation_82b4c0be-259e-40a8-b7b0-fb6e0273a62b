<?php if (isset($component)) { $__componentOriginalaa758e6a82983efcbf593f765e026bd9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa758e6a82983efcbf593f765e026bd9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => $__env->getContainer()->make(Illuminate\View\Factory::class)->make('mail::message'),'data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('mail::message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
# Payment Invoice

<?php $__env->startComponent('mail::table'); ?>
| Sold to: <?php echo new \Illuminate\Support\EncodedHtmlString($name); ?> |
| :--------- |
| Transaction ID: <?php echo new \Illuminate\Support\EncodedHtmlString($summaryData->transaction_id ?? 'N/A'); ?>|
| Date: <?php echo new \Illuminate\Support\EncodedHtmlString(\Carbon\Carbon::parse($data[0]->created_at)->format('F j, Y h:i:s A')); ?> |
| Payment Method: <?php echo new \Illuminate\Support\EncodedHtmlString($paymentIntent); ?> |
<?php echo $__env->renderComponent(); ?>

<hr>

<?php $__env->startComponent('mail::table'); ?>
| Domain | Type | Duration |Amount |
| :--------- | :------------- | :------------- |:----------- |
<?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
| <?php echo new \Illuminate\Support\EncodedHtmlString($item->name); ?> | <?php echo new \Illuminate\Support\EncodedHtmlString($item->node_type ?? 'PREMIUM'); ?> | <?php echo new \Illuminate\Support\EncodedHtmlString($item->year_length); ?> | $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($item->total_amount,2)); ?> |
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php echo $__env->renderComponent(); ?>

<hr>

<?php if($summaryData->registration_subtotal > 0): ?>
Registration Subtotal: $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($summaryData->registration_subtotal,2)); ?>

<?php endif; ?>

<?php if($summaryData->premium_subtotal > 0): ?>
Premium Subtotal: $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($summaryData->premium_subtotal,2)); ?>

<?php endif; ?>

<?php if($summaryData->subtotal > 0): ?>
Subtotal: $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($summaryData->subtotal,2)); ?>

<?php endif; ?>

<?php if($data[0]->redemption_fee > 0): ?>
## Total Redemption Fee: $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($data[0]->redemption_fee,2)); ?>

<?php endif; ?>

Miscellaneous Fees: $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($summaryData->total_fees,2)); ?> 

<hr>

## Total Paid: $<?php echo new \Illuminate\Support\EncodedHtmlString(number_format($paidAmount,2)); ?>


##### For more information, please see <?php echo new \Illuminate\Support\EncodedHtmlString(config('app.name')); ?>'s <a href="<?php echo new \Illuminate\Support\EncodedHtmlString($termsUrl); ?>">Terms and Conditions</a>.<br>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $attributes = $__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__attributesOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972)): ?>
<?php $component = $__componentOriginalf6ee7f5f2bf3e19915554c79eb482972; ?>
<?php unset($__componentOriginalf6ee7f5f2bf3e19915554c79eb482972); ?>
<?php endif; ?><?php /**PATH C:\1xampp\htdocs\sd-client\resources\views/Mails/Payment/PaymentInvoice.blade.php ENDPATH**/ ?>